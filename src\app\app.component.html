<mat-sidenav-container fullscreen>
  <mat-sidenav mode="side" [opened]="sidebarOpened">
    <mat-nav-list>
      <a mat-list-item routerLink="/dashboard">
        <mat-icon>dashboard</mat-icon>
        <span>Dashboard</span>
      </a>

      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon>people</mat-icon>
            <span>Employees</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/employees/add">
          <mat-icon>person_add</mat-icon>
          <span>Add Employee</span>
        </a>
        <a mat-list-item routerLink="/employees/upload-list">
          <mat-icon>upload_file</mat-icon>
          <span>Upload List</span>
        </a>
      </mat-expansion-panel>

      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon>description</mat-icon>
            <span>Invoices</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/invoices/upload">
          <mat-icon>upload_file</mat-icon>
          <span>Upload Invoice</span>
        </a>
      </mat-expansion-panel>

      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon>bar_chart</mat-icon>
            <span>Reports</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/reports/scheduling">
          <mat-icon>schedule</mat-icon>
          <span>Scheduling</span>
        </a>
      </mat-expansion-panel>

      <mat-expansion-panel>
        <mat-expansion-panel-header>
          <mat-panel-title>
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <a mat-list-item routerLink="/settings/company">
          <mat-icon>domain</mat-icon>
          <span>Company</span>
        </a>
        <a mat-list-item routerLink="/settings/user-roles">
          <mat-icon>supervisor_account</mat-icon>
          <span>User Roles</span>
        </a>
        <a mat-list-item routerLink="/settings/staffing-agencies">
          <mat-icon>business_center</mat-icon>
          <span>Staffing Agencies</span>
        </a>
      </mat-expansion-panel>
    </mat-nav-list>
  </mat-sidenav>

  <mat-sidenav-content>
    <mat-toolbar color="primary">
      <button mat-icon-button (click)="toggleSidebar()">
        <mat-icon>menu</mat-icon>
      </button>
      <span>Omni App</span>
    </mat-toolbar>

    <mat-progress-bar *ngIf="loadingService.loading$ | async" mode="indeterminate"></mat-progress-bar>

    <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>