import { Component, OnInit } from '@angular/core';
import { ThemeService } from './core/services/theme.service';
import { LoadingService } from './core/services/loading.service';

import { MatSidenavModule } from '@angular/material/sidenav';
import {MatListModule} from '@angular/material/list';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';

@Component({
  selector: 'omni-root',
  standalone: true,
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  imports: [
    MatSidenavModule,
    MatListModule,
    MatToolbarModule,
    MatIconModule,
    MatProgressBarModule,
    RouterOutlet,
    CommonModule,
    MatExpansionModule
  ]
})
export class AppComponent implements OnInit {
  sidebarOpened = true;

  constructor(private themeService: ThemeService, public loadingService: LoadingService) {}

  ngOnInit(): void {
    this.themeService.applyInitialTheme();
  }

  toggleSidebar(): void {
    this.sidebarOpened = !this.sidebarOpened;
  }
}
