<mat-expansion-panel [expanded]="isExpanded" (opened)="isExpanded = true" (closed)="isExpanded = false" class="dashboard-panel">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <mat-icon>bar_chart</mat-icon>
      Dashboard
    </mat-panel-title>
  </mat-expansion-panel-header>

  <div class="dashboard-content">
    <mat-tab-group [(selectedIndex)]="selectedTabIndex" animationDuration="0ms">
      <mat-tab label="Overview">
        <ng-template matTabContent>
          <div class="tab-content-padding">
            <div class="summary-cards-grid">
              <app-summary-card
                *ngFor="let metric of summaryMetrics"
                [title]="metric.title"
                [value]="metric.value"
                [valueType]="metric.valueType || 'number'"
                [iconName]="metric.iconName"
                [trendPercentage]="metric.trendPercentage"
                [trendDirection]="metric.trendDirection"
                [trendText]="metric.trendText"
                [iconBgColorClass]="metric.iconBgColorClass"
                [iconColorClass]="metric.iconColorClass"
              ></app-summary-card>
            </div>

            <div class="charts-grid">
              <mat-card class="chart-card">
                <app-chart-wrapper
                  chartTitle="Utilization by Department"
                  chartType="bar"
                  [results]="utilizationByDeptChartDataFormatted"
                  [showAxisLabels]="true"
                  [xAxisData]="utilizationByDeptAxisData"
                  [yAxisData]="['0%', '25%', '50%', '75%', '100%']"
                  yAxisLabel="Utilization %"
                  xAxisLabel="Department"
                ></app-chart-wrapper>
              </mat-card>

              <mat-card class="chart-card">
                 <app-chart-wrapper
                  chartTitle="Billable Hours Trend"
                  chartType="bar"
                  [results]="billableHoursTrendChartDataFormatted"
                  [showAxisLabels]="true"
                  [xAxisData]="billableHoursTrendAxisData"
                  [yAxisData]="['0', '400', '800', '1200', '1600']"
                  yAxisLabel="Hours"
                  xAxisLabel="Month"
                  [colorScheme]="{ domain: ['#10b981'] }"
                ></app-chart-wrapper>
              </mat-card>
            </div>

            <mat-card class="employee-type-card">
              <app-employee-type-distribution
                [distributionData]="employeeTypeDistributionData"
              ></app-employee-type-distribution>
            </mat-card>
          </div>
        </ng-template>
      </mat-tab>
      <mat-tab label="Utilization"> <ng-template matTabContent><div class="tab-content-padding">Utilization Content (TODO)</div></ng-template> </mat-tab>
      <mat-tab label="Billable Hours"> <ng-template matTabContent><div class="tab-content-padding">Billable Hours Content (TODO)</div></ng-template> </mat-tab>
      <mat-tab label="Employees"> <ng-template matTabContent><div class="tab-content-padding">Employees Content (TODO)</div></ng-template> </mat-tab>
    </mat-tab-group>
  </div>
</mat-expansion-panel>