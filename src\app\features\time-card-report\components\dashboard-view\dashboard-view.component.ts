﻿import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';

import { SummaryCardComponent } from '../../../../shared/ui/summary-card/summary-card.component';
import { ChartWrapperComponent } from '../../../../shared/ui/chart-wrapper/chart-wrapper.component';
import { EmployeeTypeDistributionComponent } from '../employee-type-distribution/employee-type-distribution.component';

import { SummaryMetric, ChartDataPoint, EmployeeTypeDistribution, MonthlyTrendDataPoint } from '../../models/dashboard-data.model';

@Component({
  selector: 'app-dashboard-view',
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    MatIconModule,
    MatTabsModule,
    MatCardModule,
    SummaryCardComponent,
    ChartWrapperComponent,
    EmployeeTypeDistributionComponent
  ],
  templateUrl: './dashboard-view.component.html',
  styleUrls: ['./dashboard-view.component.scss'],
})
export class DashboardViewComponent implements OnInit, OnChanges {
  @Input() summaryMetrics: SummaryMetric[] = [];
  @Input() utilizationByDeptData: ChartDataPoint[] = [];
  @Input() billableHoursTrendData: MonthlyTrendDataPoint[] = [];
  @Input() employeeTypeDistributionData: EmployeeTypeDistribution[] = [];

  isExpanded = true;
  selectedTabIndex = 0;

  // Formatted data for ngx-charts
  utilizationByDeptChartDataFormatted: { name: string, value: number }[] = [];
  billableHoursTrendChartDataFormatted: { name: string, value: number }[] = [];

  // Axis data for charts
  utilizationByDeptAxisData: string[] = [];
  billableHoursTrendAxisData: string[] = [];


  ngOnInit(): void {
    this.formatChartData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['summaryMetrics'] || changes['utilizationByDeptData'] || changes['billableHoursTrendData'] || changes['employeeTypeDistributionData']) {
     this.formatChartData();
    }
  }

  private formatChartData(): void {
    this.utilizationByDeptChartDataFormatted = this.utilizationByDeptData.map(item => ({
      name: item.name,
      value: item.value
    }));
    this.billableHoursTrendChartDataFormatted = this.billableHoursTrendData.map(item => ({
      name: item.name,
      value: item.value
    }));

    // Format axis data
    this.utilizationByDeptAxisData = this.utilizationByDeptData.map(item => item.name);
    this.billableHoursTrendAxisData = this.billableHoursTrendData.map(item => item.name);
  }
}