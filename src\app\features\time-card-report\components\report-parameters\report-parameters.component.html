<mat-expansion-panel [expanded]="isExpanded" (opened)="isExpanded = true" (closed)="isExpanded = false" class="report-parameters-panel">
  <mat-expansion-panel-header>
    <mat-panel-title>
      <mat-icon>tune</mat-icon>
      Report Parameters
    </mat-panel-title>
    <mat-panel-description class="panel-actions">
      <button mat-button (click)="onResetFilters($event)">
        <mat-icon>refresh</mat-icon> Reset Filters
      </button>
      <button mat-button (click)="onSaveFilters($event)">
        <mat-icon>save</mat-icon> Save Filters
      </button>
    </mat-panel-description>
  </mat-expansion-panel-header>

  <div class="parameters-content" [formGroup]="paramsForm">
    <div class="form-grid">
      <!-- Left Column -->
      <div>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Report Date</mat-label>
          <input matInput formControlName="reportDate" readonly [matDatepicker]="picker" (click)="picker.open()">
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <!-- Mock readonly style, actual date selection would change this -->
        </mat-form-field>

 <mat-form-field appearance="outline" class="full-width">
 <mat-label>Supervisors</mat-label>
 <mat-select formControlName="supervisors" multiple>
 @for (supervisor of supervisorsList; track supervisor) {
 <mat-option [value]="supervisor">{{supervisor}}</mat-option>
 }
 </mat-select>
 </mat-form-field>

 <mat-form-field appearance="outline" class="full-width">
 <mat-label>Team Leads</mat-label>
 <mat-select formControlName="teamLeads" multiple>
 @for (teamLead of teamLeadsList; track teamLead) {
 <mat-option [value]="teamLead">{{teamLead}}</mat-option>
 }
 </mat-select>
 </mat-form-field>
 </div>

 <!-- Right Column -->
 <div>
 <mat-form-field appearance="outline" class="full-width">
 <mat-label>Departments</mat-label>
 <mat-select formControlName="departments" multiple>
 @for (department of departmentsList; track department) {
 <mat-option [value]="department">{{department}}</mat-option>
 }
 </mat-select>
 </mat-form-field>

 <mat-form-field appearance="outline" class="full-width">
 <mat-label>Employment Status</mat-label>
 <mat-select formControlName="employmentStatus" multiple>
 @for (status of employmentStatusList; track status) {
 <mat-option [value]="status">{{status}}</mat-option>
 }
 </mat-select>
 </mat-form-field>

 <div formGroupName="layoutOptions" class="layout-options-group">
          <label class="group-label">Layout Options</label>
          <div class="checkbox-grid">
            <mat-checkbox formControlName="showYTD">YTD Values</mat-checkbox>
            <mat-checkbox formControlName="showQuarterly">Quarterly Values</mat-checkbox>
            <mat-checkbox formControlName="showCurrent">Current Values</mat-checkbox>
            <mat-checkbox formControlName="showMonthly">Monthly Values</mat-checkbox>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="action-buttons-footer">
    <button mat-stroked-button (click)="exportTrigger.emit()">
      <mat-icon>download</mat-icon> Export to Excel
    </button>
    <div>
      <button mat-stroked-button (click)="scheduleTrigger.emit()" class="mr-2">
        <mat-icon>notifications_none</mat-icon> Schedule Report
      </button>
      <button mat-raised-button color="primary" (click)="onRunReport()">
        <mat-icon>refresh</mat-icon> Run Report
      </button>
    </div>
  </div>
</mat-expansion-panel>