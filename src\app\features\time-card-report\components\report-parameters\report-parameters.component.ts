import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core'; // Or MatMomentDateModule

import { ReportParameters, initialReportParameters } from '../../models/report-parameters.model';

@Component({
  selector: 'app-report-parameters',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
  ],
  templateUrl: './report-parameters.component.html',
  styleUrls: ['./report-parameters.component.scss'],
})
export class ReportParametersComponent implements OnInit, OnChanges {
  @Input() initialParameters: ReportParameters = JSON.parse(JSON.stringify(initialReportParameters));
  @Output() parametersChanged = new EventEmitter<ReportParameters>();
  @Output() runReportTrigger = new EventEmitter<ReportParameters>();
  @Output() scheduleTrigger = new EventEmitter<void>();
  @Output() exportTrigger = new EventEmitter<void>();

  isExpanded = true;
  paramsForm!: FormGroup;

  constructor(private fb: FormBuilder) { }

  ngOnInit(): void {
    this.initForm(this.initialParameters);

    this.paramsForm.valueChanges.subscribe(values => {
      // Map display values back to actual arrays for emission if necessary
      const emitParams: ReportParameters = {
        reportDate: values.reportDate ? new Date(values.reportDate).toLocaleDateString('en-US') : '', // Format date
        supervisors: this.parseDisplayValue(values.supervisorsDisplay),
        teamLeads: this.parseDisplayValue(values.teamLeadsDisplay),
        departments: this.parseDisplayValue(values.departmentsDisplay),
        employmentStatus: this.parseDisplayValue(values.employmentStatusDisplay),
        layoutOptions: values.layoutOptions
      };
      this.parametersChanged.emit(emitParams);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['initialParameters'] && !changes['initialParameters'].firstChange) {
      this.initForm(changes['initialParameters'].currentValue);
    }
  }

  private initForm(params: ReportParameters): void {
    this.paramsForm = this.fb.group({
      reportDate: [params.reportDate ? new Date(params.reportDate) : null], // Use Date object for datepicker
      supervisorsDisplay: [params.supervisors.join(', ')],
      teamLeadsDisplay: [params.teamLeads.join(', ')],
      departmentsDisplay: [params.departments.join(', ')],
      employmentStatusDisplay: [params.employmentStatus.join(', ')],
      layoutOptions: this.fb.group({
        showYTD: [params.layoutOptions.showYTD],
        showQuarterly: [params.layoutOptions.showQuarterly],
        showCurrent: [params.layoutOptions.showCurrent],
        showMonthly: [params.layoutOptions.showMonthly],
      }),
    });
  }

  private parseDisplayValue(displayValue: string): string[] {
    return displayValue ? displayValue.split(',').map(s => s.trim()).filter(s => s) : [];
  }

  onResetFilters(event: MouseEvent): void {
    event.stopPropagation(); // Prevent panel toggle
    this.initForm(initialReportParameters); // Reset to global initial
    this.parametersChanged.emit(JSON.parse(JSON.stringify(initialReportParameters)));
    alert('Filters reset to default.');
  }

  onSaveFilters(event: MouseEvent): void {
    event.stopPropagation(); // Prevent panel toggle
    const filterName = prompt('Enter a name for these filter settings:', 'Saved Filter Set');
    if (filterName) {
      const currentParams = this.paramsForm.value as ReportParameters;
      // Map display values for saving if needed
      const savedFilter = {
        ...currentParams,
        name: filterName,
        supervisors: this.parseDisplayValue((currentParams as any).supervisorsDisplay),
        // ... map other display values
      };
      console.log('Saving filters:', savedFilter);
      alert(`Filters saved as "${filterName}" (logged to console).`);
    }
  }

  onRunReport(): void {
    const formValue = this.paramsForm.value;
    const reportParams: ReportParameters = {
      reportDate: formValue.reportDate ? new Date(formValue.reportDate).toLocaleDateString('en-US') : '',
      supervisors: this.parseDisplayValue(formValue.supervisorsDisplay),
      teamLeads: this.parseDisplayValue(formValue.teamLeadsDisplay),
      departments: this.parseDisplayValue(formValue.departmentsDisplay),
      employmentStatus: this.parseDisplayValue(formValue.employmentStatusDisplay),
      layoutOptions: formValue.layoutOptions
    };
    this.runReportTrigger.emit(reportParams);
  }

  // Placeholder methods for opening selectors (would involve dialogs or custom dropdowns)
  openSupervisorSelect(): void { alert('Open supervisor selection (not implemented).'); }
  openTeamLeadSelect(): void { alert('Open team lead selection (not implemented).'); }
  openDepartmentSelect(): void { alert('Open department selection (not implemented).'); }
  openEmploymentStatusSelect(): void { alert('Open employment status selection (not implemented).'); }
}