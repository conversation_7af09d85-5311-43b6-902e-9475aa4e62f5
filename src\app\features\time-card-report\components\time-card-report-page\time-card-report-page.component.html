<div class="page-container">
  <header class="page-header">
    <h1 class="page-title">Employee Time Card Utilization Report</h1>
 
<mat-button-toggle-group
[formControl]="viewModeFormControl"
aria-label="View Mode"
>
<mat-button-toggle value="dashboard">
  <mat-icon>bar_chart</mat-icon> Dashboard
</mat-button-toggle>
<mat-button-toggle value="table">
  <mat-icon>list</mat-icon> Data Table
</mat-button-toggle>
</mat-button-toggle-group>

  </header>

  <app-report-parameters
    [initialParameters]="reportParameters"
    (runReportTrigger)="onRunReport($event)"
    (scheduleTrigger)="onScheduleReport()"
    (exportTrigger)="onExportToExcel()"
    (parametersChanged)="onParametersChanged($event)"
  ></app-report-parameters>

  <app-dashboard-view
    *ngIf="viewMode === 'dashboard'"
    [summaryMetrics]="dashboardSummaryMetrics"
    [utilizationByDeptData]="utilizationByDeptChartData"
    [billableHoursTrendData]="billableHoursTrendChartData"
    [employeeTypeDistributionData]="employeeTypeDistributionData"
  ></app-dashboard-view>

  <app-data-table-view
    *ngIf="viewMode === 'table' || viewMode === 'dashboard'"
    [class.partially-hidden]="viewMode === 'dashboard'"
    [employeeData]="employeeTableData"
    (searchChanged)="onSearchChanged($event)"
  ></app-data-table-view>
</div>