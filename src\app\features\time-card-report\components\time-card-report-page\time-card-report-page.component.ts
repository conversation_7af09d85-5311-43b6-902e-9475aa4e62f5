import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatDialog } from '@angular/material/dialog';

import { ReportParametersComponent } from '../report-parameters/report-parameters.component';
import { DashboardViewComponent } from '../dashboard-view/dashboard-view.component';
import { DataTableViewComponent } from '../data-table-view/data-table-view.component';
import { ScheduleReportDialogComponent } from '../schedule-report-dialog/schedule-report-dialog.component';

import { ReportParameters, initialReportParameters } from '../../models/report-parameters.model';
import { EmployeeTimeCard } from '../../models/employee.model';
import { SummaryMetric, ChartDataPoint, EmployeeTypeDistribution, MonthlyTrendDataPoint } from '../../models/dashboard-data.model';
import { ScheduleReportConfig } from '../../models/schedule-report.model';

import {
  utilizationByDepartmentData,
  billableHoursTrendData,
  employeeTypeDistributionData as mockEmployeeTypeData,
  summaryCardsData
} from '../../../../data/reports-data';

import { EmployeeService } from '../../../employees/data-access/employee.service';

@Component({
  selector: 'app-time-card-report-page',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonToggleModule,
    MatIconModule,
    ReportParametersComponent,
    DashboardViewComponent,
    DataTableViewComponent,
  ],
  templateUrl: './time-card-report-page.component.html',
  styleUrls: ['./time-card-report-page.component.scss'],
})
export class TimeCardReportPageComponent implements OnInit {

  viewModeFormControl = new FormControl<'dashboard' | 'table'>('dashboard');

  get viewMode(): 'dashboard' | 'table' {
    const value = this.viewModeFormControl.value;
    return value === 'dashboard' || value === 'table' ? value : 'table';
  }

  setViewMode(mode: 'dashboard' | 'table'): void {
    this.viewModeFormControl.setValue(mode);
  }

  reportParameters: ReportParameters = JSON.parse(JSON.stringify(initialReportParameters));

  dashboardSummaryMetrics: SummaryMetric[] = [
    {
      title: 'Billable Hours',
      value: summaryCardsData.billableHours.value,
      iconName: 'schedule',
      trendPercentage: summaryCardsData.billableHours.trend,
      trendDirection: summaryCardsData.billableHours.trendDirection,
      iconBgColorClass: 'bg-blue-100',
      iconColorClass: 'text-blue-600',
    },
    {
      title: 'Non-Billable Hours',
      value: summaryCardsData.nonBillableHours.value,
      iconName: 'warning_amber',
      trendPercentage: summaryCardsData.nonBillableHours.trend,
      trendDirection: summaryCardsData.nonBillableHours.trendDirection,
      iconBgColorClass: 'bg-yellow-100',
      iconColorClass: 'text-yellow-600',
    },
    {
      title: 'Average Utilization',
      value: summaryCardsData.utilization.value,
      valueType: 'percent',
      iconName: 'insert_chart_outlined',
      trendPercentage: summaryCardsData.utilization.trend,
      trendDirection: summaryCardsData.utilization.trendDirection,
      iconBgColorClass: 'bg-green-100',
      iconColorClass: 'text-green-600',
    },
    {
      title: 'Revenue',
      value: summaryCardsData.revenue.value,
      valueType: 'currency',
      iconName: 'attach_money',
      trendPercentage: summaryCardsData.revenue.trend,
      trendDirection: summaryCardsData.revenue.trendDirection,
      iconBgColorClass: 'bg-green-100',
      iconColorClass: 'text-green-600',
    },
  ];

  utilizationByDeptChartData: ChartDataPoint[] = utilizationByDepartmentData.map(data => ({
    name: data.name,
    value: data.value,
  }));

  billableHoursTrendChartData: MonthlyTrendDataPoint[] = billableHoursTrendData.map(data => ({
    name: data.month,
    value: data.hours,
  }));

  employeeTypeDistributionData: EmployeeTypeDistribution[] = mockEmployeeTypeData.map(data => ({
    type: data.type,
    count: data.count,
    colorClass: 'bg-blue-500',
  }));

  employeeTableData: EmployeeTimeCard[] = [];

  constructor(
    public dialog: MatDialog,
    private employeeService: EmployeeService
  ) {}

  ngOnInit(): void {
    this.loadEmployeeData();
  }

  loadEmployeeData(): void {
    this.employeeService.getEmployees().subscribe(employees => {
      this.employeeTableData = employees.map(employee => ({
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        employeeType: employee.employeeType || '',
        department: employee.department || '',
        supervisor: employee.manager || '',
        utilization: this.calculateUtilization(employee),
        billable: this.calculateBillableHours(employee),
        nonBillable: this.calculateNonBillableHours(employee),
        totalHours: this.calculateTotalHours(employee),
        amount: this.calculateAmount(employee),
      }));
    });
  }

  private calculateUtilization(employee: any): number {
    const totalBillableHours = employee.billable || 0;
    const totalAvailableHours = 40;
    return (totalBillableHours / totalAvailableHours) * 100;
  }

  private calculateBillableHours(employee: any): number {
    return employee.billable || 0;
  }

  private calculateNonBillableHours(employee: any): number {
    const totalHours = this.calculateTotalHours(employee);
    const billableHours = this.calculateBillableHours(employee);
    return totalHours - billableHours;
  }

  private calculateTotalHours(employee: any): number {
    return 40;
  }

  private calculateAmount(employee: any): number {
    const billableHours = this.calculateBillableHours(employee);
    const hourlyRate = 100;
    return billableHours * hourlyRate;
  }

  onRunReport(params: ReportParameters): void {
    console.log('Running report with parameters:', params);
    alert('Running report... (check console for parameters)');
  }

  onScheduleReport(): void {
    const dialogRef = this.dialog.open(ScheduleReportDialogComponent, {
      width: '500px',
      data: { currentParameters: this.reportParameters },
    });

    dialogRef.afterClosed().subscribe((result: ScheduleReportConfig | undefined) => {
      if (result) {
        console.log('Schedule saved:', result);
        alert('Report schedule saved! (Check console for details)');
      }
    });
  }

  onExportToExcel(): void {
    console.log('Exporting to Excel with current parameters:', this.reportParameters);
    alert('Exporting report to Excel... (simulated)');
  }

  onParametersChanged(params: ReportParameters): void {
    this.reportParameters = params;
    console.log('Report parameters updated in parent:', params);
  }

  onSearchChanged(searchTerm: string): void {
    console.log('Search term from table view:', searchTerm);
  }
}
