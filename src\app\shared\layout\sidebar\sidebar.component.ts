import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';

interface NavLink {
  href: string;
  label: string;
  icon: string;
  subLinks?: NavLink[];
  disabled?: boolean;
}

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  standalone: true,
  imports: [RouterModule, CommonModule]
})
export class SidebarComponent {
  navLinks: NavLink[] = [
    { href: '/dashboard', label: 'Dashboard', icon: 'dashboard' },
    {
      href: '/employees',
      label: 'Employees',
      icon: 'people',
      subLinks: [
        { href: '/employees/add', label: 'Add Employee', icon: 'person_add' },
        { href: '/employees/upload/employee-list', label: 'Upload List', icon: 'upload_file' },
      ],
    },
    {
      href: '/invoices',
      label: 'Invoices',
      icon: 'receipt',
      subLinks: [
        { href: '/invoices/add/staff-log', label: 'Upload Invoice', icon: 'upload_file' },
      ],
    },
    {
      href: '/time-cards',
      label: 'Time Cards',
      icon: 'access_time',
      subLinks: [
        { href: '/time-cards', label: 'List', icon: 'list' },
        { href: '/time-cards/calendar', label: 'Calendar', icon: 'calendar_today' },
      ],
    },
    {
      href: '/reports/time-card-utilization',
      label: 'Reports',
      icon: 'bar_chart',
      subLinks: [
        { href: '/reports/time-card-utilization', label: 'Time Card Utilization', icon: 'assessment' },
        { href: '/reports/scheduling', label: 'Scheduling', icon: 'schedule' },
      ],
    },
    {
      href: '/settings',
      label: 'Settings',
      icon: 'settings',
      subLinks: [
        { href: '/settings/roles', label: 'Role Management', icon: 'admin_panel_settings' },
        { href: '/settings/timesolv', label: 'TimeSolv Integration', icon: 'sync' },
        { href: '/settings/staffing-agencies', label: 'Staffing Agencies', icon: 'work' },
        { href: '/settings/company', label: 'Company', icon: 'business' },
      ],
    },
  ];

  profileNavLinks: NavLink[] = [
    { href: '/profile', label: 'Profile', icon: 'account_circle' },
    { href: '/profile/settings', label: 'Account Settings', icon: 'settings' },
    { href: '/logout', label: 'Logout', icon: 'logout' },
  ];

  expandedItems: { [key: string]: boolean } = {};

  constructor(private router: Router) {
    // Auto-expand the section if we're on a subpage
    const currentPath = this.router.url;
    this.navLinks.forEach(link => {
      if (link.subLinks && currentPath.startsWith(link.href)) {
        this.expandedItems[link.href] = true;
      }
    });
  }

  toggleSubMenu(href: string): void {
    // Navigate to the main section page when clicking the header
    this.router.navigate([href]);
    // Toggle the submenu
    this.expandedItems[href] = !this.expandedItems[href];
  }

  isActive(href: string): boolean {
    return this.router.url === href || this.router.url.startsWith(href + '/');
  }
}
